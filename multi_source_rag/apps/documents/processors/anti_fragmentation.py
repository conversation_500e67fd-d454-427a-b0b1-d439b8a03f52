"""
Anti-Fragmentation Document Processor

This module prevents document fragmentation through intelligent merging and relationship detection.
It implements the architect's recommendations for cross-document relationships and semantic overlap detection.

Key Features:
- Semantic overlap detection to prevent duplicate content
- Cross-platform relationship extraction (GitHub refs in Slack, etc.)
- Conversation continuity validation
- Intelligent document merging based on semantic similarity
- Temporal relationship detection for conversation flows
"""

import logging
import re
from collections import defaultdict
from typing import Any, Dict, List, Tuple, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class AntiFragmentationProcessor:
    """
    Prevents document fragmentation through intelligent merging and relationship detection.
    
    This processor implements the architect's recommendations for:
    1. Semantic overlap detection
    2. Cross-platform relationship extraction  
    3. Conversation continuity validation
    4. Intelligent document merging
    """
    
    def __init__(self, tenant_slug: str):
        """
        Initialize the anti-fragmentation processor.
        
        Args:
            tenant_slug: Tenant identifier for scoped processing
        """
        self.tenant_slug = tenant_slug
        self.similarity_threshold = 0.85  # High overlap threshold
        self.temporal_gap_hours = 2  # Max gap for conversation continuations
        self.min_merge_tokens = 100  # Minimum tokens to consider for merging
        
    def process_document_batch(self, documents: List[Dict]) -> List[Dict]:
        """
        Process a batch of documents with anti-fragmentation measures.
        
        Args:
            documents: List of document dictionaries to process
            
        Returns:
            List of processed documents with reduced fragmentation
        """
        if not documents:
            return documents
            
        logger.info(f"Processing {len(documents)} documents for anti-fragmentation")
        
        # Step 1: Detect semantic overlaps
        overlaps = self._detect_semantic_overlaps(documents)
        logger.info(f"Detected {len(overlaps)} semantic overlaps")
        
        # Step 2: Merge highly similar documents
        merged_docs = self._merge_overlapping_documents(documents, overlaps)
        logger.info(f"Merged documents: {len(documents)} → {len(merged_docs)}")
        
        # Step 3: Detect conversation continuations
        continuations = self._detect_conversation_continuations(merged_docs)
        logger.info(f"Detected {len(continuations)} conversation continuations")
        
        # Step 4: Merge conversation continuations
        final_docs = self._merge_conversation_continuations(merged_docs, continuations)
        logger.info(f"Final documents after continuation merging: {len(final_docs)}")
        
        # Step 5: Extract cross-document relationships
        relationships = self._extract_cross_platform_relationships(final_docs)
        logger.info(f"Extracted {len(relationships)} cross-platform relationships")
        
        # Step 6: Store relationships in document metadata
        self._store_relationships_in_metadata(final_docs, relationships)
        
        return final_docs
    
    def _detect_semantic_overlaps(self, documents: List[Dict]) -> List[Tuple[str, str, float]]:
        """
        Detect documents with high semantic overlap that should be merged.
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            List of tuples (doc1_id, doc2_id, similarity_score)
        """
        overlaps = []
        
        for i, doc1 in enumerate(documents):
            for j, doc2 in enumerate(documents[i+1:], i+1):
                similarity = self._calculate_semantic_similarity(
                    doc1.get('content', ''), 
                    doc2.get('content', '')
                )
                
                if similarity > self.similarity_threshold:
                    overlaps.append((doc1.get('id'), doc2.get('id'), similarity))
                    
        return overlaps
    
    def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate semantic similarity between two texts.
        
        Args:
            text1: First text to compare
            text2: Second text to compare
            
        Returns:
            Similarity score between 0 and 1
        """
        if not text1 or not text2:
            return 0.0
            
        # Simple implementation using word overlap
        # In production, this could use embedding similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
            
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _merge_overlapping_documents(
        self, 
        documents: List[Dict], 
        overlaps: List[Tuple[str, str, float]]
    ) -> List[Dict]:
        """
        Merge documents with high semantic overlap.
        
        Args:
            documents: Original documents
            overlaps: List of overlapping document pairs
            
        Returns:
            List of documents with overlaps merged
        """
        if not overlaps:
            return documents
            
        # Create document lookup
        doc_lookup = {doc.get('id'): doc for doc in documents}
        merged_ids = set()
        merged_docs = []
        
        # Group overlapping documents
        overlap_groups = self._group_overlapping_documents(overlaps)
        
        for group in overlap_groups:
            if len(group) > 1:
                # Merge documents in this group
                primary_doc = doc_lookup[group[0]]
                merged_content = [primary_doc.get('content', '')]
                merged_metadata = primary_doc.get('metadata', {}).copy()
                
                for doc_id in group[1:]:
                    if doc_id in doc_lookup:
                        doc = doc_lookup[doc_id]
                        merged_content.append(doc.get('content', ''))
                        # Merge metadata
                        for key, value in doc.get('metadata', {}).items():
                            if key not in merged_metadata:
                                merged_metadata[key] = value
                        merged_ids.add(doc_id)
                
                # Create merged document
                merged_doc = primary_doc.copy()
                merged_doc['content'] = '\n\n'.join(merged_content)
                merged_doc['metadata'] = merged_metadata
                merged_doc['metadata']['merged_from'] = group[1:]
                merged_doc['metadata']['is_merged'] = True
                
                merged_docs.append(merged_doc)
                merged_ids.add(group[0])
        
        # Add non-merged documents
        for doc in documents:
            if doc.get('id') not in merged_ids:
                merged_docs.append(doc)
                
        return merged_docs
    
    def _group_overlapping_documents(self, overlaps: List[Tuple[str, str, float]]) -> List[List[str]]:
        """
        Group overlapping documents into merge groups.
        
        Args:
            overlaps: List of overlapping document pairs
            
        Returns:
            List of document ID groups to merge
        """
        # Build adjacency graph
        graph = defaultdict(set)
        for doc1, doc2, _ in overlaps:
            graph[doc1].add(doc2)
            graph[doc2].add(doc1)
        
        # Find connected components
        visited = set()
        groups = []
        
        for doc_id in graph:
            if doc_id not in visited:
                group = []
                self._dfs_group(doc_id, graph, visited, group)
                if len(group) > 1:
                    groups.append(group)
                    
        return groups
    
    def _dfs_group(self, doc_id: str, graph: Dict, visited: set, group: List[str]):
        """Depth-first search to find connected document groups."""
        visited.add(doc_id)
        group.append(doc_id)
        
        for neighbor in graph[doc_id]:
            if neighbor not in visited:
                self._dfs_group(neighbor, graph, visited, group)
    
    def _detect_conversation_continuations(self, documents: List[Dict]) -> List[Tuple[str, str]]:
        """
        Detect conversation continuations that should be merged.
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            List of tuples (predecessor_id, successor_id)
        """
        continuations = []
        
        # Sort documents by timestamp for temporal analysis
        timestamped_docs = []
        for doc in documents:
            timestamp = self._extract_timestamp(doc)
            if timestamp:
                timestamped_docs.append((timestamp, doc))
        
        timestamped_docs.sort(key=lambda x: x[0])
        
        # Look for temporal continuations
        for i in range(len(timestamped_docs) - 1):
            current_time, current_doc = timestamped_docs[i]
            next_time, next_doc = timestamped_docs[i + 1]
            
            time_gap = (next_time - current_time).total_seconds() / 3600  # hours
            
            if time_gap <= self.temporal_gap_hours:
                # Check if they're related conversations
                if self._are_conversation_related(current_doc, next_doc):
                    continuations.append((current_doc.get('id'), next_doc.get('id')))
                    
        return continuations
    
    def _extract_timestamp(self, document: Dict) -> Optional[datetime]:
        """Extract timestamp from document metadata."""
        metadata = document.get('metadata', {})
        
        # Try different timestamp fields
        for field in ['start_time', 'created_at', 'timestamp', 'date']:
            if field in metadata:
                try:
                    if isinstance(metadata[field], str):
                        return datetime.fromisoformat(metadata[field].replace('Z', '+00:00'))
                    elif isinstance(metadata[field], datetime):
                        return metadata[field]
                except (ValueError, TypeError):
                    continue
                    
        return None
    
    def _are_conversation_related(self, doc1: Dict, doc2: Dict) -> bool:
        """
        Check if two documents are part of related conversations.
        
        Args:
            doc1: First document
            doc2: Second document
            
        Returns:
            True if documents are conversation-related
        """
        # Check for common participants
        participants1 = set(doc1.get('metadata', {}).get('participants', []))
        participants2 = set(doc2.get('metadata', {}).get('participants', []))
        
        if participants1 and participants2:
            overlap = len(participants1.intersection(participants2))
            total = len(participants1.union(participants2))
            if total > 0 and overlap / total > 0.5:  # 50% participant overlap
                return True
        
        # Check for common topics/keywords
        content1 = doc1.get('content', '').lower()
        content2 = doc2.get('content', '').lower()
        
        # Simple keyword overlap check
        words1 = set(content1.split())
        words2 = set(content2.split())
        
        if words1 and words2:
            overlap = len(words1.intersection(words2))
            if overlap > 10:  # Significant word overlap
                return True
                
        return False
    
    def _merge_conversation_continuations(
        self, 
        documents: List[Dict], 
        continuations: List[Tuple[str, str]]
    ) -> List[Dict]:
        """
        Merge conversation continuations into coherent documents.
        
        Args:
            documents: List of documents
            continuations: List of continuation pairs
            
        Returns:
            List of documents with continuations merged
        """
        if not continuations:
            return documents
            
        doc_lookup = {doc.get('id'): doc for doc in documents}
        merged_ids = set()
        merged_docs = []
        
        # Build continuation chains
        chains = self._build_continuation_chains(continuations)
        
        for chain in chains:
            if len(chain) > 1:
                # Merge documents in this chain
                primary_doc = doc_lookup[chain[0]]
                merged_content = []
                merged_metadata = primary_doc.get('metadata', {}).copy()
                
                for doc_id in chain:
                    if doc_id in doc_lookup:
                        doc = doc_lookup[doc_id]
                        merged_content.append(doc.get('content', ''))
                        merged_ids.add(doc_id)
                
                # Create merged document
                merged_doc = primary_doc.copy()
                merged_doc['content'] = '\n\n---\n\n'.join(merged_content)
                merged_doc['metadata'] = merged_metadata
                merged_doc['metadata']['continuation_chain'] = chain[1:]
                merged_doc['metadata']['is_continuation_merged'] = True
                
                merged_docs.append(merged_doc)
        
        # Add non-merged documents
        for doc in documents:
            if doc.get('id') not in merged_ids:
                merged_docs.append(doc)
                
        return merged_docs
    
    def _build_continuation_chains(self, continuations: List[Tuple[str, str]]) -> List[List[str]]:
        """Build chains of continuing conversations."""
        # Build graph of continuations
        graph = defaultdict(list)
        in_degree = defaultdict(int)
        
        for pred, succ in continuations:
            graph[pred].append(succ)
            in_degree[succ] += 1
            if pred not in in_degree:
                in_degree[pred] = 0
        
        # Find chain starts (nodes with in_degree 0)
        chains = []
        for node in in_degree:
            if in_degree[node] == 0:
                chain = []
                current = node
                while current:
                    chain.append(current)
                    # Get next node (assuming linear chains)
                    next_nodes = graph[current]
                    current = next_nodes[0] if next_nodes else None
                if len(chain) > 1:
                    chains.append(chain)
                    
        return chains

    def _extract_cross_platform_relationships(self, documents: List[Dict]) -> List[Dict]:
        """
        Extract relationships between different platforms (GitHub refs in Slack, etc.).

        Args:
            documents: List of document dictionaries

        Returns:
            List of relationship dictionaries
        """
        relationships = []

        for doc in documents:
            content = doc.get('content', '')
            doc_id = doc.get('id')

            # GitHub references in Slack
            github_patterns = [
                r'github\.com/[\w-]+/[\w-]+/(?:pull|issues)/(\d+)',
                r'(?:PR|pull request)\s*#?(\d+)',
                r'(?:issue|bug)\s*#?(\d+)'
            ]

            for pattern in github_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    relationships.append({
                        'source_doc': doc_id,
                        'target_type': 'github',
                        'target_id': match,
                        'relationship_type': 'references',
                        'confidence': 0.9
                    })

            # Slack thread references
            slack_patterns = [
                r'slack\.com/archives/([^/]+)/p(\d+)',
                r'thread from (\d{4}-\d{2}-\d{2})',
                r'mentioned in #([\w-]+)'
            ]

            for pattern in slack_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    relationships.append({
                        'source_doc': doc_id,
                        'target_type': 'slack',
                        'target_id': match,
                        'relationship_type': 'thread_reference',
                        'confidence': 0.8
                    })

        return relationships

    def _store_relationships_in_metadata(self, documents: List[Dict], relationships: List[Dict]):
        """
        Store extracted relationships in document metadata.

        Args:
            documents: List of document dictionaries
            relationships: List of relationship dictionaries
        """
        # Group relationships by source document
        doc_relationships = defaultdict(list)
        for rel in relationships:
            doc_relationships[rel['source_doc']].append(rel)

        # Add relationships to document metadata
        for doc in documents:
            doc_id = doc.get('id')
            if doc_id in doc_relationships:
                if 'metadata' not in doc:
                    doc['metadata'] = {}
                doc['metadata']['cross_platform_relationships'] = doc_relationships[doc_id]

    def validate_conversation_continuity(self, documents: List[Dict]) -> Dict[str, Any]:
        """
        Validate that conversation flows are preserved across documents.

        Args:
            documents: List of document dictionaries

        Returns:
            Validation results with issues and continuity score
        """
        issues = []
        continuity_score = 0

        for doc in documents:
            strategy = doc.get('metadata', {}).get('chunking_strategy', '')

            if strategy == 'hybrid_conversation_aware':
                # Check for conversation completeness
                if self._has_orphaned_replies(doc):
                    issues.append(f"Document {doc.get('id')} has orphaned replies")

                if self._has_incomplete_threads(doc):
                    issues.append(f"Document {doc.get('id')} has incomplete threads")

                # Calculate conversation flow score
                flow_score = self._calculate_conversation_flow_score(doc)
                continuity_score += flow_score

        avg_continuity = continuity_score / len(documents) if documents else 0

        return {
            'issues': issues,
            'continuity_score': avg_continuity,
            'is_valid': len(issues) == 0 and avg_continuity > 0.8
        }

    def _has_orphaned_replies(self, document: Dict) -> bool:
        """Check if document has replies without parent messages."""
        content = document.get('content', '')

        # Look for reply patterns without context
        reply_patterns = [
            r'^@\w+\s+',  # Mentions at start of line
            r'^\s*>\s*',  # Quote blocks
            r'In reply to',  # Explicit reply indicators
        ]

        for pattern in reply_patterns:
            if re.search(pattern, content, re.MULTILINE):
                # Check if there's sufficient context
                if len(content.split('\n')) < 3:  # Very short content with replies
                    return True

        return False

    def _has_incomplete_threads(self, document: Dict) -> bool:
        """Check if document has incomplete thread conversations."""
        metadata = document.get('metadata', {})

        # Check thread metadata
        thread_count = metadata.get('thread_count', 0)
        message_count = metadata.get('message_count', 0)

        if thread_count > 0 and message_count < thread_count * 2:
            # Threads with very few messages might be incomplete
            return True

        return False

    def _calculate_conversation_flow_score(self, document: Dict) -> float:
        """Calculate how well conversation flow is preserved in document."""
        content = document.get('content', '')
        metadata = document.get('metadata', {})

        score = 0.0

        # Check for temporal coherence
        if 'start_time' in metadata and 'end_time' in metadata:
            score += 0.3  # Has temporal boundaries

        # Check for participant consistency
        participants = metadata.get('participants', [])
        if len(participants) > 1:
            score += 0.3  # Multi-participant conversation

        # Check for thread preservation
        if metadata.get('thread_count', 0) > 0:
            score += 0.2  # Has threads

        # Check content coherence (simple heuristic)
        lines = content.split('\n')
        if len(lines) > 5:  # Sufficient content
            score += 0.2

        return min(score, 1.0)
