#!/usr/bin/env python3
"""
Test script for ingestion service improvements based on architect's review.

This script validates:
1. Embedding consistency enforcement
2. Anti-fragmentation processing
3. Cross-document relationship extraction
4. Conversation continuity validation

Usage:
    python scripts/test_ingestion_improvements.py
"""

import os
import sys
import django
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "multi_source_rag"))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import logging
from typing import Dict, List, Any
from apps.documents.services.ingestion_service import IngestionService
from apps.documents.processors.anti_fragmentation import AntiFragmentationProcessor
from apps.core.utils.embedding_consistency import validate_embedding_consistency

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_embedding_consistency():
    """Test embedding consistency validation."""
    print("\n🔍 Testing Embedding Consistency Validation...")
    
    try:
        is_consistent = validate_embedding_consistency()
        if is_consistent:
            print("✅ Embedding consistency validation: PASSED")
        else:
            print("❌ Embedding consistency validation: FAILED")
        return is_consistent
    except Exception as e:
        print(f"❌ Embedding consistency validation error: {e}")
        return False


def test_ingestion_service_initialization():
    """Test ingestion service initialization with embedding validation."""
    print("\n🔍 Testing Ingestion Service Initialization...")
    
    try:
        # This should validate embedding consistency at startup
        service = IngestionService(tenant='stride')
        print("✅ Ingestion service initialization: PASSED")
        return True
    except RuntimeError as e:
        if "Embedding models are inconsistent" in str(e):
            print(f"✅ Embedding consistency enforcement: WORKING (caught inconsistency)")
            return True
        else:
            print(f"❌ Unexpected runtime error: {e}")
            return False
    except Exception as e:
        print(f"❌ Ingestion service initialization error: {e}")
        return False


def test_anti_fragmentation_processor():
    """Test anti-fragmentation document processing."""
    print("\n🔍 Testing Anti-Fragmentation Processor...")

    # Create test documents with overlaps and relationships
    test_documents = [
        {
            'id': 'doc1',
            'content': 'This is a discussion about the authentication bug in PR #123. We need to fix this issue.',
            'metadata': {
                'participants': ['alice', 'bob'],
                'start_time': '2024-01-15T10:00:00Z',
                'chunking_strategy': 'hybrid_conversation_aware'
            }
        },
        {
            'id': 'doc2',
            'content': 'The authentication bug in PR #123 is causing login failures. This issue needs immediate attention.',
            'metadata': {
                'participants': ['alice', 'charlie'],
                'start_time': '2024-01-15T10:30:00Z',
                'chunking_strategy': 'hybrid_conversation_aware'
            }
        },
        {
            'id': 'doc3',
            'content': 'Follow-up on the authentication discussion. The fix has been deployed.',
            'metadata': {
                'participants': ['alice', 'bob'],
                'start_time': '2024-01-15T11:00:00Z',
                'chunking_strategy': 'hybrid_conversation_aware'
            }
        }
    ]
    
    try:
        processor = AntiFragmentationProcessor('stride')
        processed_docs = processor.process_document_batch(test_documents)
        
        print(f"✅ Document processing: {len(test_documents)} → {len(processed_docs)} documents")
        
        # Check for relationships
        relationships_found = 0
        for doc in processed_docs:
            if 'cross_platform_relationships' in doc.get('metadata', {}):
                relationships_found += len(doc['metadata']['cross_platform_relationships'])
        
        print(f"✅ Cross-platform relationships extracted: {relationships_found}")
        
        # Test conversation continuity validation
        validation_result = processor.validate_conversation_continuity(processed_docs)
        print(f"✅ Conversation continuity score: {validation_result['continuity_score']:.2f}")
        
        if validation_result['issues']:
            print(f"⚠️  Continuity issues found: {len(validation_result['issues'])}")
        else:
            print("✅ No conversation continuity issues found")
            
        return True
        
    except Exception as e:
        print(f"❌ Anti-fragmentation processor error: {e}")
        return False


def test_semantic_overlap_detection():
    """Test semantic overlap detection."""
    print("\n🔍 Testing Semantic Overlap Detection...")
    
    # Create documents with high semantic overlap
    overlapping_docs = [
        {
            'id': 'overlap1',
            'content': 'The authentication system is failing for users trying to log in to the application.',
            'metadata': {}
        },
        {
            'id': 'overlap2',
            'content': 'Users are unable to log in due to authentication system failures in the application.',
            'metadata': {}
        },
        {
            'id': 'different',
            'content': 'The new feature for file uploads has been completed and is ready for testing.',
            'metadata': {}
        }
    ]
    
    try:
        processor = AntiFragmentationProcessor('stride')
        overlaps = processor._detect_semantic_overlaps(overlapping_docs)
        
        print(f"✅ Semantic overlaps detected: {len(overlaps)}")
        
        for doc1_id, doc2_id, similarity in overlaps:
            print(f"   - {doc1_id} ↔ {doc2_id}: {similarity:.2f} similarity")
        
        # Test merging
        merged_docs = processor._merge_overlapping_documents(overlapping_docs, overlaps)
        print(f"✅ Document merging: {len(overlapping_docs)} → {len(merged_docs)} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Semantic overlap detection error: {e}")
        return False


def test_cross_platform_relationships():
    """Test cross-platform relationship extraction."""
    print("\n🔍 Testing Cross-Platform Relationship Extraction...")
    
    test_docs = [
        {
            'id': 'slack_msg',
            'content': 'Hey team, I found a bug in PR #456. Can someone review github.com/company/repo/pull/456?',
            'metadata': {}
        },
        {
            'id': 'github_ref',
            'content': 'This issue was mentioned in #engineering-team channel on Slack.',
            'metadata': {}
        }
    ]
    
    try:
        processor = AntiFragmentationProcessor('stride')
        relationships = processor._extract_cross_platform_relationships(test_docs)
        
        print(f"✅ Cross-platform relationships found: {len(relationships)}")
        
        for rel in relationships:
            print(f"   - {rel['source_doc']} → {rel['target_type']} #{rel['target_id']} ({rel['relationship_type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Cross-platform relationship extraction error: {e}")
        return False


def test_token_limit_enforcement():
    """Test that anti-fragmentation processor respects token limits."""
    print("\n🔍 Testing Token Limit Enforcement...")

    # Create documents that would exceed token limit if merged
    large_content = "This is a very long document about authentication issues. " * 100  # ~5000+ characters

    test_documents = [
        {
            'id': 'large_doc1',
            'content': large_content,
            'metadata': {'participants': ['alice', 'bob']}
        },
        {
            'id': 'large_doc2',
            'content': large_content,  # Same content = high overlap
            'metadata': {'participants': ['alice', 'charlie']}
        }
    ]

    try:
        # Test with small token limit
        processor = AntiFragmentationProcessor('stride', max_tokens=200)

        # Check individual document token counts
        doc1_tokens = processor._estimate_tokens(test_documents[0]['content'])
        doc2_tokens = processor._estimate_tokens(test_documents[1]['content'])
        total_tokens = doc1_tokens + doc2_tokens

        print(f"   Document 1 tokens: {doc1_tokens}")
        print(f"   Document 2 tokens: {doc2_tokens}")
        print(f"   Total if merged: {total_tokens}")
        print(f"   Token limit: {processor.max_tokens}")

        # Process documents
        processed_docs = processor.process_document_batch(test_documents)

        # Verify token limits are respected
        for doc in processed_docs:
            estimated_tokens = doc.get('metadata', {}).get('estimated_tokens',
                                                          processor._estimate_tokens(doc.get('content', '')))
            if estimated_tokens > processor.max_tokens:
                print(f"❌ Token limit exceeded: {estimated_tokens} > {processor.max_tokens}")
                return False

        print(f"✅ Token limits respected: {len(test_documents)} → {len(processed_docs)} documents")
        print(f"✅ No document exceeds {processor.max_tokens} token limit")

        return True

    except Exception as e:
        print(f"❌ Token limit enforcement test error: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive test of all improvements."""
    print("🚀 Testing Ingestion Service Improvements Based on Architect's Review")
    print("=" * 70)
    
    tests = [
        ("Embedding Consistency", test_embedding_consistency),
        ("Ingestion Service Init", test_ingestion_service_initialization),
        ("Anti-Fragmentation", test_anti_fragmentation_processor),
        ("Semantic Overlap", test_semantic_overlap_detection),
        ("Cross-Platform Relations", test_cross_platform_relationships),
        ("Token Limit Enforcement", test_token_limit_enforcement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All improvements working correctly!")
        print("The ingestion service now implements the architect's recommendations:")
        print("  ✅ Embedding consistency enforcement")
        print("  ✅ Anti-fragmentation processing")
        print("  ✅ Cross-document relationship extraction")
        print("  ✅ Semantic overlap detection")
        print("  ✅ Conversation continuity validation")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Review implementation.")
    
    return passed == total


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
